/**
 * Workspace Database Service
 * Handles all workspace-related database operations using Appwrite
 */

import {
  adminDatabases,
  createSessionServices,
  AppwriteServerError,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult, QueryParams } from './base';
import {
  Workspace,
  WorkspaceConfiguration,
  WorkspaceStats,
  WorkspacePermission,
  WorkspaceCollaborator,
  CreateWorkspaceRequest,
  UpdateWorkspaceRequest,
  ShareWorkspaceRequest,
  WorkspaceType,
  WorkspaceStatus,
  WorkspaceVisibility,
  WorkspaceRole,
  FilterParams
} from '@/types/workspace';

// Database configuration
export const WORKSPACE_DATABASE_ID = 'omnispace-workspaces';
export const WORKSPACE_COLLECTIONS = {
  WORKSPACES: 'workspaces',
  WORKSPACE_PERMISSIONS: 'workspace-permissions',
  WORKSPACE_COLLABORATORS: 'workspace-collaborators',
  WORKSPACE_STATS: 'workspace-stats',
  WORKSPACE_TEMPLATES: 'workspace-templates',
  WORKSPACE_FILES: 'workspace-files',
  WORKSPACE_EXECUTIONS: 'workspace-executions',
  WORKSPACE_SESSIONS: 'workspace-sessions'
} as const;

// Document interfaces for Appwrite
export interface WorkspaceDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  // Workspace fields
  name: string;
  description?: string;
  type: WorkspaceType;
  status: WorkspaceStatus;
  visibility: WorkspaceVisibility;
  
  // Owner information
  ownerId: string;
  ownerName: string;
  
  // Configuration (stored as JSON string)
  configuration: string;
  templateId?: string;
  
  // Container information
  containerId?: string;
  containerStatus?: string;
  accessUrl?: string;
  vncPort?: number;
  
  // File system
  rootPath: string;
  fileCount: number;
  totalSize: number;
  
  // Organization
  tags: string[];
  category?: string;
  
  // AI features
  aiEnabled: boolean;
  aiAssistantConfig?: string; // JSON string
  
  // Timestamps
  lastAccessedAt?: string;
}

export interface WorkspacePermissionDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  workspaceId: string;
  userId: string;
  userName: string;
  userEmail: string;
  role: WorkspaceRole;
  permissions: string; // JSON string of Permission[]
  grantedBy: string;
  expiresAt?: string;
}

export interface WorkspaceCollaboratorDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  workspaceId: string;
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  role: WorkspaceRole;
  status: 'online' | 'offline' | 'away';
  lastActivity: string;
  
  // Current state (JSON strings)
  cursor?: string;
  selection?: string;
}

export interface WorkspaceStatsDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  workspaceId: string;
  totalRuntime: number;
  cpuUsage: number;
  memoryUsage: number;
  storageUsage: number;
  networkUsage: number;
  lastActivity: string;
  sessionCount: number;
  fileOperations: number;
  codeExecutions: number;
}

// Service parameters
export interface CreateWorkspaceParams extends CreateWorkspaceRequest {
  ownerId: string;
  ownerName: string;
}

export interface UpdateWorkspaceParams extends UpdateWorkspaceRequest {
  workspaceId: string;
}

export interface WorkspaceQueryParams extends QueryParams {
  filters?: FilterParams;
}

// Workspace Database Service
export class WorkspaceService extends BaseAppwriteService {
  constructor() {
    super('WorkspaceService');
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminDatabases.get(WORKSPACE_DATABASE_ID);
      return true;
    } catch (error) {
      logger.error('Workspace service health check failed', { error });
      return false;
    }
  }

  // ============================================================================
  // Workspace CRUD Operations
  // ============================================================================

  /**
   * Create a new workspace
   */
  async createWorkspace(params: CreateWorkspaceParams, sessionId?: string): Promise<ServiceResult<Workspace>> {
    return this.executeOperation('createWorkspace', async () => {
      this.validateRequired(params, ['name', 'type', 'ownerId', 'ownerName']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
      const workspaceId = ID.unique();

      // Default configuration
      const defaultConfiguration: WorkspaceConfiguration = {
        runtime: {
          type: params.type,
          version: 'latest',
          environment: {},
          dependencies: []
        },
        resources: {
          cpu: 2,
          memory: 2048,
          storage: 10
        },
        security: {
          allowedPorts: [3000, 8000, 8080],
          restrictedCommands: ['rm -rf', 'sudo rm'],
          enableSudo: false,
          enableNetworking: true,
          enableFileUpload: true,
          enableFileDownload: true
        },
        editor: {
          theme: 'dark',
          fontSize: 14,
          tabSize: 2,
          wordWrap: true,
          minimap: true,
          lineNumbers: true,
          autoSave: true,
          autoComplete: true
        },
        collaboration: {
          enabled: false,
          maxCollaborators: 5,
          allowAnonymous: false,
          requireApproval: true,
          chatEnabled: true,
          voiceEnabled: false,
          screenShareEnabled: false
        }
      };

      // Merge with provided configuration
      const finalConfiguration = {
        ...defaultConfiguration,
        ...params.configuration
      };

      // Create workspace document
      const workspaceDoc: Omit<WorkspaceDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
        name: params.name,
        description: params.description,
        type: params.type,
        status: 'creating',
        visibility: params.visibility || 'private',
        ownerId: params.ownerId,
        ownerName: params.ownerName,
        configuration: JSON.stringify(finalConfiguration),
        templateId: params.templateId,
        rootPath: `/workspaces/${workspaceId}`,
        fileCount: 0,
        totalSize: 0,
        tags: params.tags || [],
        category: this.getCategoryFromType(params.type),
        aiEnabled: true,
        aiAssistantConfig: JSON.stringify({
          enabled: true,
          model: 'gpt-4',
          features: {
            codeCompletion: true,
            codeAnalysis: true,
            errorDetection: true,
            refactoring: true,
            documentation: true,
            testing: true,
            debugging: true,
            terminalAssistance: true
          },
          contextWindow: 8000,
          temperature: 0.7
        })
      };

      const document = await services.databases.createDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        workspaceId,
        workspaceDoc,
        [
          `read("user:${params.ownerId}")`,
          `write("user:${params.ownerId}")`,
          `delete("user:${params.ownerId}")`
        ]
      );

      // Create initial stats document
      await this.createWorkspaceStats(workspaceId, sessionId);

      // Create owner permission
      await this.addWorkspacePermission({
        workspaceId,
        userId: params.ownerId,
        userName: params.ownerName,
        userEmail: '', // Will be filled by the calling service
        role: 'owner',
        permissions: this.getDefaultPermissions('owner'),
        grantedBy: params.ownerId
      }, sessionId);

      logger.info(`Workspace created: ${workspaceId} by ${params.ownerId}`);
      return this.documentToWorkspace(document as unknown as WorkspaceDocument);
    });
  }

  /**
   * Get workspace by ID
   */
  async getWorkspace(workspaceId: string, sessionId?: string): Promise<ServiceResult<Workspace>> {
    return this.executeOperation('getWorkspace', async () => {
      this.validateRequired({ workspaceId }, ['workspaceId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const document = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        workspaceId
      );

      return this.documentToWorkspace(document as unknown as WorkspaceDocument);
    });
  }

  /**
   * List workspaces with filtering and pagination
   */
  async listWorkspaces(
    queryParams?: WorkspaceQueryParams,
    sessionId?: string
  ): Promise<ServiceResult<PaginatedResult<Workspace>>> {
    return this.executeOperation('listWorkspaces', async () => {
      const validatedPagination = this.validatePagination(queryParams?.pagination);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Build queries
      const queries: string[] = [];
      
      if (queryParams?.filters) {
        const filters = queryParams.filters;
        
        if (filters.type?.length) {
          queries.push(`equal("type", [${filters.type.map(t => `"${t}"`).join(', ')}])`);
        }
        
        if (filters.status?.length) {
          queries.push(`equal("status", [${filters.status.map(s => `"${s}"`).join(', ')}])`);
        }
        
        if (filters.visibility?.length) {
          queries.push(`equal("visibility", [${filters.visibility.map(v => `"${v}"`).join(', ')}])`);
        }
        
        if (filters.ownerId) {
          queries.push(`equal("ownerId", "${filters.ownerId}")`);
        }
        
        if (filters.search) {
          queries.push(`search("name", "${filters.search}")`);
        }
        
        if (filters.tags?.length) {
          filters.tags.forEach(tag => {
            queries.push(`equal("tags", "${tag}")`);
          });
        }
      }

      // Add pagination
      queries.push(`limit(${validatedPagination.limit})`);
      queries.push(`offset(${validatedPagination.offset})`);

      // Add ordering
      if (validatedPagination.orderBy) {
        const direction = validatedPagination.orderDirection === 'desc' ? 'orderDesc' : 'orderAsc';
        queries.push(`${direction}("${validatedPagination.orderBy}")`);
      } else {
        queries.push('orderDesc("$updatedAt")');
      }

      const result = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        queries
      );

      const workspaces = await Promise.all(
        result.documents.map(doc => this.documentToWorkspace(doc as unknown as WorkspaceDocument))
      );

      return this.formatPaginatedResult(workspaces, result.total, validatedPagination);
    });
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private getCategoryFromType(type: WorkspaceType): string {
    const categories = {
      python: 'Development',
      nodejs: 'Development',
      general: 'General',
      collaborative: 'Collaboration'
    };
    return categories[type] || 'General';
  }

  private getDefaultPermissions(role: WorkspaceRole) {
    // This would return appropriate permissions based on role
    // Implementation depends on your permission system
    return [];
  }

  private async createWorkspaceStats(workspaceId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
    
    const statsDoc: Omit<WorkspaceStatsDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
      workspaceId,
      totalRuntime: 0,
      cpuUsage: 0,
      memoryUsage: 0,
      storageUsage: 0,
      networkUsage: 0,
      lastActivity: new Date().toISOString(),
      sessionCount: 0,
      fileOperations: 0,
      codeExecutions: 0
    };

    await services.databases.createDocument(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
      ID.unique(),
      statsDoc,
      [`read("user:*")`, `write("user:*")`]
    );
  }

  private async addWorkspacePermission(
    params: {
      workspaceId: string;
      userId: string;
      userName: string;
      userEmail: string;
      role: WorkspaceRole;
      permissions: any[];
      grantedBy: string;
      expiresAt?: Date;
    },
    sessionId?: string
  ): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
    
    const permissionDoc: Omit<WorkspacePermissionDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
      workspaceId: params.workspaceId,
      userId: params.userId,
      userName: params.userName,
      userEmail: params.userEmail,
      role: params.role,
      permissions: JSON.stringify(params.permissions),
      grantedBy: params.grantedBy,
      expiresAt: params.expiresAt?.toISOString()
    };

    await services.databases.createDocument(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
      ID.unique(),
      permissionDoc,
      [`read("user:${params.userId}")`, `write("user:${params.grantedBy}")`]
    );
  }

  private async documentToWorkspace(doc: WorkspaceDocument): Promise<Workspace> {
    // Convert Appwrite document to Workspace interface
    const configuration = JSON.parse(doc.configuration) as WorkspaceConfiguration;
    const aiAssistantConfig = doc.aiAssistantConfig ? JSON.parse(doc.aiAssistantConfig) : undefined;

    // Fetch related data (stats, permissions, collaborators)
    const [stats, permissions, collaborators] = await Promise.all([
      this.getWorkspaceStats(doc.$id),
      this.getWorkspacePermissions(doc.$id),
      this.getWorkspaceCollaborators(doc.$id)
    ]);

    return {
      id: doc.$id,
      name: doc.name,
      description: doc.description,
      type: doc.type,
      status: doc.status,
      visibility: doc.visibility,
      ownerId: doc.ownerId,
      ownerName: doc.ownerName,
      permissions: permissions || [],
      configuration,
      containerId: doc.containerId,
      containerStatus: doc.containerStatus,
      accessUrl: doc.accessUrl,
      vncPort: doc.vncPort,
      createdAt: new Date(doc.$createdAt),
      updatedAt: new Date(doc.$updatedAt),
      lastAccessedAt: doc.lastAccessedAt ? new Date(doc.lastAccessedAt) : undefined,
      stats: stats || this.getDefaultStats(),
      collaborators: collaborators || [],
      activeUsers: [], // This would be populated from real-time data
      rootPath: doc.rootPath,
      fileCount: doc.fileCount,
      totalSize: doc.totalSize,
      tags: doc.tags,
      category: doc.category,
      aiEnabled: doc.aiEnabled,
      aiAssistantConfig
    };
  }

  private async getWorkspaceStats(workspaceId: string): Promise<WorkspaceStats | null> {
    try {
      const result = await adminDatabases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
        [`equal("workspaceId", "${workspaceId}")`, 'limit(1)']
      );

      if (result.documents.length === 0) return null;

      const doc = result.documents[0] as WorkspaceStatsDocument;
      return {
        totalRuntime: doc.totalRuntime,
        cpuUsage: doc.cpuUsage,
        memoryUsage: doc.memoryUsage,
        storageUsage: doc.storageUsage,
        networkUsage: doc.networkUsage,
        lastActivity: new Date(doc.lastActivity),
        sessionCount: doc.sessionCount,
        fileOperations: doc.fileOperations,
        codeExecutions: doc.codeExecutions
      };
    } catch (error) {
      logger.error('Failed to fetch workspace stats', { workspaceId, error });
      return null;
    }
  }

  private async getWorkspacePermissions(workspaceId: string): Promise<WorkspacePermission[] | null> {
    try {
      const result = await adminDatabases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
        [`equal("workspaceId", "${workspaceId}")`]
      );

      return result.documents.map((doc: WorkspacePermissionDocument) => ({
        userId: doc.userId,
        userName: doc.userName,
        userEmail: doc.userEmail,
        role: doc.role,
        permissions: JSON.parse(doc.permissions),
        grantedAt: new Date(doc.$createdAt),
        grantedBy: doc.grantedBy,
        expiresAt: doc.expiresAt ? new Date(doc.expiresAt) : undefined
      }));
    } catch (error) {
      logger.error('Failed to fetch workspace permissions', { workspaceId, error });
      return null;
    }
  }

  private async getWorkspaceCollaborators(workspaceId: string): Promise<WorkspaceCollaborator[] | null> {
    try {
      const result = await adminDatabases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
        [`equal("workspaceId", "${workspaceId}")`]
      );

      return result.documents.map((doc: WorkspaceCollaboratorDocument) => ({
        userId: doc.userId,
        userName: doc.userName,
        userEmail: doc.userEmail,
        userAvatar: doc.userAvatar,
        role: doc.role,
        status: doc.status,
        joinedAt: new Date(doc.$createdAt),
        lastActivity: new Date(doc.lastActivity),
        cursor: doc.cursor ? JSON.parse(doc.cursor) : undefined,
        selection: doc.selection ? JSON.parse(doc.selection) : undefined
      }));
    } catch (error) {
      logger.error('Failed to fetch workspace collaborators', { workspaceId, error });
      return null;
    }
  }

  private getDefaultStats(): WorkspaceStats {
    return {
      totalRuntime: 0,
      cpuUsage: 0,
      memoryUsage: 0,
      storageUsage: 0,
      networkUsage: 0,
      lastActivity: new Date(),
      sessionCount: 0,
      fileOperations: 0,
      codeExecutions: 0
    };
  }

  // ============================================================================
  // Update and Delete Operations
  // ============================================================================

  /**
   * Update workspace
   */
  async updateWorkspace(params: UpdateWorkspaceParams, sessionId?: string): Promise<ServiceResult<Workspace>> {
    return this.executeOperation('updateWorkspace', async () => {
      this.validateRequired(params, ['workspaceId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Prepare update data
      const updateData: Partial<WorkspaceDocument> = {};

      if (params.name !== undefined) updateData.name = params.name;
      if (params.description !== undefined) updateData.description = params.description;
      if (params.visibility !== undefined) updateData.visibility = params.visibility;
      if (params.tags !== undefined) updateData.tags = params.tags;

      if (params.configuration) {
        // Get current configuration and merge
        const currentDoc = await services.databases.getDocument(
          WORKSPACE_DATABASE_ID,
          WORKSPACE_COLLECTIONS.WORKSPACES,
          params.workspaceId
        ) as WorkspaceDocument;

        const currentConfig = JSON.parse(currentDoc.configuration) as WorkspaceConfiguration;
        const mergedConfig = { ...currentConfig, ...params.configuration };
        updateData.configuration = JSON.stringify(mergedConfig);
      }

      const document = await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        params.workspaceId,
        updateData
      );

      logger.info(`Workspace updated: ${params.workspaceId}`);
      return this.documentToWorkspace(document as WorkspaceDocument);
    });
  }

  /**
   * Delete workspace
   */
  async deleteWorkspace(workspaceId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteWorkspace', async () => {
      this.validateRequired({ workspaceId }, ['workspaceId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Delete related documents first
      await Promise.all([
        this.deleteWorkspaceStats(workspaceId, sessionId),
        this.deleteWorkspacePermissions(workspaceId, sessionId),
        this.deleteWorkspaceCollaborators(workspaceId, sessionId)
      ]);

      // Delete main workspace document
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        workspaceId
      );

      logger.info(`Workspace deleted: ${workspaceId}`);
      return true;
    });
  }

  // ============================================================================
  // Collaboration Operations
  // ============================================================================

  /**
   * Share workspace with users
   */
  async shareWorkspace(
    workspaceId: string,
    params: ShareWorkspaceRequest,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('shareWorkspace', async () => {
      this.validateRequired({ workspaceId, ...params }, ['workspaceId', 'userIds', 'role']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Get workspace to verify ownership/permissions
      const workspace = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        workspaceId
      ) as WorkspaceDocument;

      // Add permissions for each user
      for (const userId of params.userIds) {
        // Check if permission already exists
        const existingPermissions = await services.databases.listDocuments(
          WORKSPACE_DATABASE_ID,
          WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
          [
            `equal("workspaceId", "${workspaceId}")`,
            `equal("userId", "${userId}")`,
            'limit(1)'
          ]
        );

        if (existingPermissions.documents.length === 0) {
          // Create new permission
          await this.addWorkspacePermission({
            workspaceId,
            userId,
            userName: '', // Would be fetched from user service
            userEmail: '', // Would be fetched from user service
            role: params.role,
            permissions: this.getDefaultPermissions(params.role),
            grantedBy: workspace.ownerId,
            expiresAt: params.expiresAt
          }, sessionId);

          // Add as collaborator
          await this.addWorkspaceCollaborator({
            workspaceId,
            userId,
            userName: '', // Would be fetched from user service
            userEmail: '', // Would be fetched from user service
            role: params.role
          }, sessionId);
        }
      }

      logger.info(`Workspace shared: ${workspaceId} with ${params.userIds.length} users`);
      return true;
    });
  }

  /**
   * Remove user from workspace
   */
  async removeCollaborator(
    workspaceId: string,
    userId: string,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('removeCollaborator', async () => {
      this.validateRequired({ workspaceId, userId }, ['workspaceId', 'userId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Remove permission
      const permissions = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
        [
          `equal("workspaceId", "${workspaceId}")`,
          `equal("userId", "${userId}")`
        ]
      );

      for (const permission of permissions.documents) {
        await services.databases.deleteDocument(
          WORKSPACE_DATABASE_ID,
          WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
          permission.$id
        );
      }

      // Remove collaborator
      const collaborators = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
        [
          `equal("workspaceId", "${workspaceId}")`,
          `equal("userId", "${userId}")`
        ]
      );

      for (const collaborator of collaborators.documents) {
        await services.databases.deleteDocument(
          WORKSPACE_DATABASE_ID,
          WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
          collaborator.$id
        );
      }

      logger.info(`Collaborator removed: ${userId} from workspace ${workspaceId}`);
      return true;
    });
  }

  // ============================================================================
  // Status and Statistics Operations
  // ============================================================================

  /**
   * Update workspace status
   */
  async updateWorkspaceStatus(
    workspaceId: string,
    status: WorkspaceStatus,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('updateWorkspaceStatus', async () => {
      this.validateRequired({ workspaceId, status }, ['workspaceId', 'status']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACES,
        workspaceId,
        { status }
      );

      logger.info(`Workspace status updated: ${workspaceId} -> ${status}`);
      return true;
    });
  }

  /**
   * Update workspace statistics
   */
  async updateWorkspaceStats(
    workspaceId: string,
    stats: Partial<WorkspaceStats>,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('updateWorkspaceStats', async () => {
      this.validateRequired({ workspaceId }, ['workspaceId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Find existing stats document
      const existingStats = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
        [`equal("workspaceId", "${workspaceId}")`, 'limit(1)']
      );

      if (existingStats.documents.length > 0) {
        const updateData: Partial<WorkspaceStatsDocument> = {};

        if (stats.totalRuntime !== undefined) updateData.totalRuntime = stats.totalRuntime;
        if (stats.cpuUsage !== undefined) updateData.cpuUsage = stats.cpuUsage;
        if (stats.memoryUsage !== undefined) updateData.memoryUsage = stats.memoryUsage;
        if (stats.storageUsage !== undefined) updateData.storageUsage = stats.storageUsage;
        if (stats.networkUsage !== undefined) updateData.networkUsage = stats.networkUsage;
        if (stats.sessionCount !== undefined) updateData.sessionCount = stats.sessionCount;
        if (stats.fileOperations !== undefined) updateData.fileOperations = stats.fileOperations;
        if (stats.codeExecutions !== undefined) updateData.codeExecutions = stats.codeExecutions;

        updateData.lastActivity = new Date().toISOString();

        await services.databases.updateDocument(
          WORKSPACE_DATABASE_ID,
          WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
          existingStats.documents[0].$id,
          updateData
        );
      } else {
        // Create new stats document if it doesn't exist
        await this.createWorkspaceStats(workspaceId, sessionId);
      }

      return true;
    });
  }

  // ============================================================================
  // Helper Methods for Related Documents
  // ============================================================================

  private async deleteWorkspaceStats(workspaceId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const stats = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
      [`equal("workspaceId", "${workspaceId}")`]
    );

    for (const stat of stats.documents) {
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_STATS,
        stat.$id
      );
    }
  }

  private async deleteWorkspacePermissions(workspaceId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const permissions = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
      [`equal("workspaceId", "${workspaceId}")`]
    );

    for (const permission of permissions.documents) {
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_PERMISSIONS,
        permission.$id
      );
    }
  }

  private async deleteWorkspaceCollaborators(workspaceId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const collaborators = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
      [`equal("workspaceId", "${workspaceId}")`]
    );

    for (const collaborator of collaborators.documents) {
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
        collaborator.$id
      );
    }
  }

  private async addWorkspaceCollaborator(
    params: {
      workspaceId: string;
      userId: string;
      userName: string;
      userEmail: string;
      role: WorkspaceRole;
    },
    sessionId?: string
  ): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const collaboratorDoc: Omit<WorkspaceCollaboratorDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
      workspaceId: params.workspaceId,
      userId: params.userId,
      userName: params.userName,
      userEmail: params.userEmail,
      role: params.role,
      status: 'offline',
      lastActivity: new Date().toISOString()
    };

    await services.databases.createDocument(
      WORKSPACE_DATABASE_ID,
      WORKSPACE_COLLECTIONS.WORKSPACE_COLLABORATORS,
      ID.unique(),
      collaboratorDoc,
      [`read("user:${params.userId}")`, `write("user:${params.userId}")`]
    );
  }
}
