/**
 * Workspace Template Service
 * Handles workspace template management using Appwrite
 */

import {
  adminDatabases,
  createSessionServices,
  AppwriteServerError,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult } from './base';
import {
  WorkspaceTemplate,
  TemplateFile,
  DirectoryStructure,
  TemplateReview,
  WorkspaceType,
  WorkspaceConfiguration
} from '@/types/workspace';
import { WORKSPACE_DATABASE_ID, WORKSPACE_COLLECTIONS } from './workspace';

// Template document interface for Appwrite
export interface WorkspaceTemplateDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  // Basic information
  name: string;
  description: string;
  type: WorkspaceType;
  category: string;
  
  // Template configuration (JSON string)
  configuration: string;
  
  // Files and structure (JSON strings)
  files: string; // TemplateFile[]
  structure: string; // DirectoryStructure
  
  // Metadata
  version: string;
  author: string;
  authorId: string;
  
  // Usage statistics
  usageCount: number;
  rating: number;
  
  // Visibility and sharing
  visibility: 'public' | 'private' | 'team';
  tags: string[];
  
  // Requirements (JSON string)
  requirements: string;
  
  // Preview
  screenshots: string[];
  demoUrl?: string;
  
  // Installation
  installScript?: string;
  setupInstructions?: string;
}

export interface TemplateReviewDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  templateId: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
}

// Service parameters
export interface CreateTemplateParams {
  name: string;
  description: string;
  type: WorkspaceType;
  category: string;
  configuration: Partial<WorkspaceConfiguration>;
  files: TemplateFile[];
  structure: DirectoryStructure;
  version: string;
  authorId: string;
  author: string;
  visibility?: 'public' | 'private' | 'team';
  tags?: string[];
  requirements?: {
    minCpu: number;
    minMemory: number;
    minStorage: number;
    dependencies: string[];
  };
  screenshots?: string[];
  demoUrl?: string;
  installScript?: string;
  setupInstructions?: string;
}

export interface UpdateTemplateParams {
  templateId: string;
  name?: string;
  description?: string;
  configuration?: Partial<WorkspaceConfiguration>;
  files?: TemplateFile[];
  structure?: DirectoryStructure;
  version?: string;
  visibility?: 'public' | 'private' | 'team';
  tags?: string[];
  screenshots?: string[];
  demoUrl?: string;
  installScript?: string;
  setupInstructions?: string;
}

export interface TemplateQueryParams {
  type?: WorkspaceType[];
  category?: string[];
  visibility?: ('public' | 'private' | 'team')[];
  tags?: string[];
  search?: string;
  authorId?: string;
  minRating?: number;
  pagination?: PaginationParams;
}

// Workspace Template Service
export class WorkspaceTemplateService extends BaseAppwriteService {
  constructor() {
    super('WorkspaceTemplateService');
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminDatabases.get(WORKSPACE_DATABASE_ID);
      return true;
    } catch (error) {
      logger.error('Workspace template service health check failed', { error });
      return false;
    }
  }

  // ============================================================================
  // Template CRUD Operations
  // ============================================================================

  /**
   * Create a new workspace template
   */
  async createTemplate(params: CreateTemplateParams, sessionId?: string): Promise<ServiceResult<WorkspaceTemplate>> {
    return this.executeOperation('createTemplate', async () => {
      this.validateRequired(params, ['name', 'description', 'type', 'category', 'authorId', 'author']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
      const templateId = ID.unique();

      // Default requirements
      const defaultRequirements = {
        minCpu: 1,
        minMemory: 1024,
        minStorage: 5,
        dependencies: []
      };

      // Create template document
      const templateDoc: Omit<WorkspaceTemplateDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
        name: params.name,
        description: params.description,
        type: params.type,
        category: params.category,
        configuration: JSON.stringify(params.configuration),
        files: JSON.stringify(params.files),
        structure: JSON.stringify(params.structure),
        version: params.version,
        author: params.author,
        authorId: params.authorId,
        usageCount: 0,
        rating: 0,
        visibility: params.visibility || 'private',
        tags: params.tags || [],
        requirements: JSON.stringify(params.requirements || defaultRequirements),
        screenshots: params.screenshots || [],
        demoUrl: params.demoUrl,
        installScript: params.installScript,
        setupInstructions: params.setupInstructions
      };

      const document = await services.databases.createDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId,
        templateDoc,
        [
          `read("user:${params.authorId}")`,
          `write("user:${params.authorId}")`,
          ...(params.visibility === 'public' ? ['read("any")'] : [])
        ]
      );

      logger.info(`Template created: ${templateId} by ${params.authorId}`);
      return this.documentToWorkspaceTemplate(document as WorkspaceTemplateDocument);
    });
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string, sessionId?: string): Promise<ServiceResult<WorkspaceTemplate>> {
    return this.executeOperation('getTemplate', async () => {
      this.validateRequired({ templateId }, ['templateId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const document = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId
      );

      return this.documentToWorkspaceTemplate(document as WorkspaceTemplateDocument);
    });
  }

  /**
   * List templates with filtering and pagination
   */
  async listTemplates(
    queryParams?: TemplateQueryParams,
    sessionId?: string
  ): Promise<ServiceResult<PaginatedResult<WorkspaceTemplate>>> {
    return this.executeOperation('listTemplates', async () => {
      const validatedPagination = this.validatePagination(queryParams?.pagination);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Build queries
      const queries: string[] = [];

      if (queryParams?.type?.length) {
        queries.push(`equal("type", [${queryParams.type.map(t => `"${t}"`).join(', ')}])`);
      }

      if (queryParams?.category?.length) {
        queries.push(`equal("category", [${queryParams.category.map(c => `"${c}"`).join(', ')}])`);
      }

      if (queryParams?.visibility?.length) {
        queries.push(`equal("visibility", [${queryParams.visibility.map(v => `"${v}"`).join(', ')}])`);
      }

      if (queryParams?.authorId) {
        queries.push(`equal("authorId", "${queryParams.authorId}")`);
      }

      if (queryParams?.minRating) {
        queries.push(`greaterThanEqual("rating", ${queryParams.minRating})`);
      }

      if (queryParams?.search) {
        queries.push(`search("name", "${queryParams.search}")`);
      }

      if (queryParams?.tags?.length) {
        queryParams.tags.forEach(tag => {
          queries.push(`equal("tags", "${tag}")`);
        });
      }

      // Add pagination
      queries.push(`limit(${validatedPagination.limit})`);
      queries.push(`offset(${validatedPagination.offset})`);
      queries.push('orderDesc("rating")'); // Order by rating by default

      const result = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        queries
      );

      const templates = await Promise.all(
        result.documents.map(doc => this.documentToWorkspaceTemplate(doc as WorkspaceTemplateDocument))
      );

      return this.formatPaginatedResult(templates, result.total, validatedPagination);
    });
  }

  /**
   * Update template
   */
  async updateTemplate(params: UpdateTemplateParams, sessionId?: string): Promise<ServiceResult<WorkspaceTemplate>> {
    return this.executeOperation('updateTemplate', async () => {
      this.validateRequired(params, ['templateId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Prepare update data
      const updateData: Partial<WorkspaceTemplateDocument> = {};

      if (params.name !== undefined) updateData.name = params.name;
      if (params.description !== undefined) updateData.description = params.description;
      if (params.visibility !== undefined) updateData.visibility = params.visibility;
      if (params.tags !== undefined) updateData.tags = params.tags;
      if (params.screenshots !== undefined) updateData.screenshots = params.screenshots;
      if (params.demoUrl !== undefined) updateData.demoUrl = params.demoUrl;
      if (params.installScript !== undefined) updateData.installScript = params.installScript;
      if (params.setupInstructions !== undefined) updateData.setupInstructions = params.setupInstructions;

      if (params.configuration !== undefined) {
        updateData.configuration = JSON.stringify(params.configuration);
      }

      if (params.files !== undefined) {
        updateData.files = JSON.stringify(params.files);
      }

      if (params.structure !== undefined) {
        updateData.structure = JSON.stringify(params.structure);
      }

      if (params.version !== undefined) {
        updateData.version = params.version;
      }

      const document = await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        params.templateId,
        updateData
      );

      logger.info(`Template updated: ${params.templateId}`);
      return this.documentToWorkspaceTemplate(document as WorkspaceTemplateDocument);
    });
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteTemplate', async () => {
      this.validateRequired({ templateId }, ['templateId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Delete template reviews first
      await this.deleteTemplateReviews(templateId, sessionId);

      // Delete main template document
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId
      );

      logger.info(`Template deleted: ${templateId}`);
      return true;
    });
  }

  // ============================================================================
  // Template Usage and Reviews
  // ============================================================================

  /**
   * Increment template usage count
   */
  async incrementUsageCount(templateId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('incrementUsageCount', async () => {
      this.validateRequired({ templateId }, ['templateId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Get current template
      const template = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId
      ) as WorkspaceTemplateDocument;

      // Update usage count
      await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId,
        { usageCount: template.usageCount + 1 }
      );

      logger.info(`Template usage incremented: ${templateId}`);
      return true;
    });
  }

  /**
   * Add template review
   */
  async addReview(
    templateId: string,
    userId: string,
    userName: string,
    rating: number,
    comment: string,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('addReview', async () => {
      this.validateRequired({ templateId, userId, rating }, ['templateId', 'userId', 'rating']);

      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Check if user already reviewed this template
      const existingReviews = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        'template-reviews', // Would need to be added to WORKSPACE_COLLECTIONS
        [
          `equal("templateId", "${templateId}")`,
          `equal("userId", "${userId}")`,
          'limit(1)'
        ]
      );

      if (existingReviews.documents.length > 0) {
        // Update existing review
        await services.databases.updateDocument(
          WORKSPACE_DATABASE_ID,
          'template-reviews',
          existingReviews.documents[0].$id,
          { rating, comment }
        );
      } else {
        // Create new review
        const reviewDoc: Omit<TemplateReviewDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
          templateId,
          userId,
          userName,
          rating,
          comment
        };

        await services.databases.createDocument(
          WORKSPACE_DATABASE_ID,
          'template-reviews',
          ID.unique(),
          reviewDoc,
          [`read("any")`, `write("user:${userId}")`]
        );
      }

      // Update template average rating
      await this.updateTemplateRating(templateId, sessionId);

      logger.info(`Review added for template: ${templateId} by ${userId}`);
      return true;
    });
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async deleteTemplateReviews(templateId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const reviews = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      'template-reviews',
      [`equal("templateId", "${templateId}")`]
    );

    for (const review of reviews.documents) {
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        'template-reviews',
        review.$id
      );
    }
  }

  private async updateTemplateRating(templateId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    // Get all reviews for this template
    const reviews = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      'template-reviews',
      [`equal("templateId", "${templateId}")`]
    );

    if (reviews.documents.length > 0) {
      const totalRating = reviews.documents.reduce((sum: number, review: any) => sum + review.rating, 0);
      const averageRating = Math.round((totalRating / reviews.documents.length) * 10) / 10;

      await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_TEMPLATES,
        templateId,
        { rating: averageRating }
      );
    }
  }

  private async getTemplateReviews(templateId: string): Promise<TemplateReview[]> {
    try {
      const result = await adminDatabases.listDocuments(
        WORKSPACE_DATABASE_ID,
        'template-reviews',
        [`equal("templateId", "${templateId}")`, 'orderDesc("$createdAt")']
      );

      return result.documents.map((doc: TemplateReviewDocument) => ({
        id: doc.$id,
        userId: doc.userId,
        userName: doc.userName,
        rating: doc.rating,
        comment: doc.comment,
        createdAt: new Date(doc.$createdAt)
      }));
    } catch (error) {
      logger.error('Failed to fetch template reviews', { templateId, error });
      return [];
    }
  }

  private async documentToWorkspaceTemplate(doc: WorkspaceTemplateDocument): Promise<WorkspaceTemplate> {
    // Get template reviews
    const reviews = await this.getTemplateReviews(doc.$id);

    return {
      id: doc.$id,
      name: doc.name,
      description: doc.description,
      type: doc.type,
      category: doc.category,
      configuration: JSON.parse(doc.configuration),
      files: JSON.parse(doc.files),
      structure: JSON.parse(doc.structure),
      version: doc.version,
      author: doc.author,
      authorId: doc.authorId,
      createdAt: new Date(doc.$createdAt),
      updatedAt: new Date(doc.$updatedAt),
      usageCount: doc.usageCount,
      rating: doc.rating,
      reviews,
      visibility: doc.visibility,
      tags: doc.tags,
      requirements: JSON.parse(doc.requirements),
      screenshots: doc.screenshots,
      demoUrl: doc.demoUrl,
      installScript: doc.installScript,
      setupInstructions: doc.setupInstructions
    };
  }
}
