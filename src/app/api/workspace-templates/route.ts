/**
 * Workspace Templates API Routes
 * Handles workspace template management
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { workspaceTemplateService } from '@/services/appwrite';
import { authMiddleware, optionalAuthMiddleware } from '@/lib/middleware/auth';
import { rateLimitMiddleware } from '@/lib/middleware/rate-limit';
import { WorkspaceType } from '@/types/workspace';

// Validation schemas
const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name too long'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description too long'),
  type: z.enum(['python', 'nodejs', 'general', 'collaborative'] as const),
  category: z.string().min(1, 'Category is required'),
  configuration: z.object({
    runtime: z.object({
      version: z.string().optional(),
      environment: z.record(z.string(), z.string()).optional(),
      dependencies: z.array(z.string()).optional()
    }).optional(),
    resources: z.object({
      cpu: z.number().min(1).max(16).optional(),
      memory: z.number().min(512).max(32768).optional(),
      storage: z.number().min(1).max(1000).optional()
    }).optional(),
    editor: z.object({
      theme: z.string().optional(),
      fontSize: z.number().min(8).max(32).optional(),
      tabSize: z.number().min(1).max(8).optional()
    }).optional(),
    collaboration: z.object({
      enabled: z.boolean().optional(),
      maxCollaborators: z.number().min(1).max(50).optional()
    }).optional()
  }),
  files: z.array(z.object({
    path: z.string(),
    content: z.string(),
    type: z.enum(['file', 'directory'] as const),
    permissions: z.string().optional(),
    template: z.boolean().optional()
  })),
  structure: z.object({
    name: z.string(),
    type: z.enum(['file', 'directory'] as const),
    children: z.array(z.any()).optional(),
    template: z.boolean().optional()
  }),
  version: z.string().min(1, 'Version is required'),
  visibility: z.enum(['public', 'private', 'team'] as const).optional(),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
  requirements: z.object({
    minCpu: z.number().min(1),
    minMemory: z.number().min(512),
    minStorage: z.number().min(1),
    dependencies: z.array(z.string())
  }).optional(),
  screenshots: z.array(z.string().min(1)).max(5, 'Too many screenshots').optional(),
  demoUrl: z.string().min(1).optional(),
  installScript: z.string().optional(),
  setupInstructions: z.string().optional()
});

const updateTemplateSchema = createTemplateSchema.partial().extend({
  templateId: z.string().min(1, 'Template ID is required')
});

const addReviewSchema = z.object({
  rating: z.number().min(1).max(5),
  comment: z.string().max(1000, 'Comment too long')
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function getUserInfo(request: NextRequest): { userId: string; userEmail: string } | null {
  const userId = request.headers.get('x-user-id');
  const userEmail = request.headers.get('x-user-email');
  
  if (!userId || !userEmail) return null;
  
  return { userId, userEmail };
}

function handleResponse(result: any) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });
  } else {
    const status = result.error?.code === 'AUTHENTICATION_REQUIRED' ? 401 :
                  result.error?.code === 'INSUFFICIENT_PERMISSIONS' ? 403 :
                  result.error?.code === 'TEMPLATE_NOT_FOUND' ? 404 :
                  result.error?.code === 'VALIDATION_ERROR' ? 400 : 500;
    
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Unknown error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        details: result.error?.details
      }
    }, { status });
  }
}

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // Moderate limit for template operations
  message: 'Too many template requests'
};

// POST /api/workspace-templates - Create a new template
export async function POST(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const body = await authReq.json();
        const validatedData = createTemplateSchema.parse(body);
        
        const userInfo = getUserInfo(authReq);
        if (!userInfo) {
          return NextResponse.json({
            success: false,
            error: { message: 'User information not found', code: 'USER_INFO_MISSING' }
          }, { status: 400 });
        }

        const sessionId = getSessionId(authReq);
        
        // Create template with user information
        const createParams = {
          ...validatedData,
          authorId: userInfo.userId,
          author: userInfo.userEmail.split('@')[0] // Use email prefix as author name
        };

        const result = await workspaceTemplateService.createTemplate(createParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Template POST API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// GET /api/workspace-templates - List templates with filtering and pagination
export async function GET(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return optionalAuthMiddleware(req, async (authReq) => {
      try {
        const { searchParams } = new URL(authReq.url);
        const sessionId = getSessionId(authReq);

        // Parse query parameters
        const page = parseInt(searchParams.get('page') || '1');
        const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
        const search = searchParams.get('search') || undefined;
        const type = searchParams.get('type') as WorkspaceType | undefined;
        const category = searchParams.get('category') || undefined;
        const visibility = searchParams.get('visibility') as 'public' | 'private' | 'team' | undefined;
        const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined;
        const authorId = searchParams.get('authorId') || undefined;
        const minRating = searchParams.get('minRating') ? parseFloat(searchParams.get('minRating')!) : undefined;

        // Build query parameters
        const queryParams = {
          type: type ? [type] : undefined,
          category: category ? [category] : undefined,
          visibility: visibility ? [visibility] : undefined,
          tags,
          search,
          authorId,
          minRating,
          pagination: {
            limit,
            offset: (page - 1) * limit
          }
        };

        const result = await workspaceTemplateService.listTemplates(queryParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Template GET API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PUT /api/workspace-templates - Update template
export async function PUT(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const body = await authReq.json();
        const validatedData = updateTemplateSchema.parse(body);
        const sessionId = getSessionId(authReq);

        const { templateId, ...updateData } = validatedData;
        const updateParams = {
          templateId,
          ...updateData
        };

        const result = await workspaceTemplateService.updateTemplate(updateParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Template PUT API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// DELETE /api/workspace-templates - Delete template
export async function DELETE(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { searchParams } = new URL(authReq.url);
        const templateId = searchParams.get('id');

        if (!templateId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Template ID is required', code: 'MISSING_TEMPLATE_ID' }
          }, { status: 400 });
        }

        const sessionId = getSessionId(authReq);
        const result = await workspaceTemplateService.deleteTemplate(templateId, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Template DELETE API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}
